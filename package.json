{"name": "thepelota", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.70.0", "@types/react-paginate": "^7.1.4", "@upstash/redis": "^1.35.0", "classnames": "^2.5.1", "fast-xml-parser": "^5.2.5", "next": "15.2.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-loading-skeleton": "^3.5.0", "react-paginate": "^8.3.0", "react-youtube": "^10.1.0", "use-debounce": "^10.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "15.2.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.5.3", "stylelint": "^16.17.0", "stylelint-config-clean-order": "^7.0.0", "stylelint-config-standard": "^37.0.0", "stylelint-prettier": "^5.0.3", "typescript": "^5"}}