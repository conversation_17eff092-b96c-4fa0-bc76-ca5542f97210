'use client'

import {useEffect, useRef, useState} from 'react'
import Skeleton from 'react-loading-skeleton'
import 'react-loading-skeleton/dist/skeleton.css'

interface LazyImageProps {
    src: string
    alt: string
    className?: string
    style?: React.CSSProperties
    children?: React.ReactNode
    fallbackSrc?: string
    loadingDelay?: number
}

export default function LazyImage({
    src,
    alt,
    className,
    style,
    children,
    fallbackSrc,
    loadingDelay = 0,
}: LazyImageProps) {
    const [isLoaded, setIsLoaded] = useState(false)
    const [hasError, setHasError] = useState(false)
    const [shouldLoad, setShouldLoad] = useState(false)
    const [retryCount, setRetryCount] = useState(0)
    const imgRef = useRef<HTMLDivElement>(null)

    // Default fallback image (a simple gray placeholder)
    const defaultFallback =
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzI3IiBoZWlnaHQ9IjE4NCIgdmlld0JveD0iMCAwIDMyNyAxODQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjciIGhlaWdodD0iMTg0IiBmaWxsPSIjMjAyMDIwIi8+CjxwYXRoIGQ9Ik0xNjMuNSA5MkwxNDMuNSA3MkgxODMuNUwxNjMuNSA5MloiIGZpbGw9IiM0NDQ0NDQiLz4KPC9zdmc+'

    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        // Add loading delay for staggered loading effect
                        if (loadingDelay > 0) {
                            setTimeout(() => setShouldLoad(true), loadingDelay)
                        } else {
                            setShouldLoad(true)
                        }
                        observer.unobserve(entry.target)
                    }
                })
            },
            {
                rootMargin: '200px', // Start loading earlier for better LCP
                threshold: 0.01, // Lower threshold for faster triggering
            }
        )

        if (imgRef.current) {
            observer.observe(imgRef.current)
        }

        return () => {
            if (imgRef.current) {
                observer.unobserve(imgRef.current)
            }
        }
    }, [loadingDelay])

    const handleImageLoad = () => {
        setIsLoaded(true)
        setHasError(false)
    }

    const handleImageError = () => {
        console.warn(`Failed to load image: ${src}`)

        // Try retry first (up to 2 times)
        if (retryCount < 2) {
            setRetryCount((prev) => prev + 1)
            setTimeout(
                () => {
                    setShouldLoad(true)
                },
                1000 * (retryCount + 1)
            ) // Exponential backoff
            return
        }

        setHasError(true)
        setIsLoaded(false)
    }

    // Determine which image URL to use with WebP optimization
    const getImageUrl = () => {
        if (hasError) {
            return fallbackSrc || defaultFallback
        }

        // Try to use higher quality YouTube thumbnails with WebP support
        if (src.includes('youtube.com') || src.includes('ytimg.com')) {
            // Use maxresdefault for better quality, fallback to hqdefault
            const videoId = src.match(/\/vi\/([^\/]+)\//)?.[1]
            if (videoId) {
                return `https://i.ytimg.com/vi_webp/${videoId}/hqdefault.webp`
            }
        }

        return src
    }

    const imageUrl = getImageUrl()

    return (
        <div
            ref={imgRef}
            className={className}
            style={{
                ...style,
                position: 'relative',
                overflow: 'hidden',
                backgroundImage: isLoaded ? `url(${imageUrl})` : undefined,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center',
                backgroundSize: 'cover',
            }}
        >
            {/* Show skeleton while not loaded */}
            {!isLoaded && (
                <div style={{position: 'relative'}}>
                    <Skeleton
                        height='100%'
                        width='100%'
                        style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            zIndex: 1,
                        }}
                        baseColor='#202020'
                        highlightColor='#444'
                        borderRadius={8}
                    />
                    {/* Show retry indicator */}
                    {retryCount > 0 && (
                        <div
                            style={{
                                position: 'absolute',
                                top: '50%',
                                left: '50%',
                                transform: 'translate(-50%, -50%)',
                                zIndex: 2,
                                color: '#fff',
                                fontSize: '12px',
                                background: 'rgba(0,0,0,0.7)',
                                padding: '4px 8px',
                                borderRadius: '4px',
                            }}
                        >
                            Retrying... ({retryCount}/2)
                        </div>
                    )}
                </div>
            )}

            {/* Hidden image for loading */}
            {shouldLoad && (
                <img
                    src={imageUrl}
                    alt={alt}
                    onLoad={handleImageLoad}
                    onError={handleImageError}
                    style={{
                        position: 'absolute',
                        opacity: 0,
                        pointerEvents: 'none',
                        width: '1px',
                        height: '1px',
                    }}
                />
            )}

            {/* Children (like duration badge) */}
            {children && (
                <div
                    style={{
                        position: 'relative',
                        zIndex: 2,
                        height: '100%',
                        display: 'flex',
                        alignItems: 'flex-end',
                        justifyContent: 'flex-end',
                        padding: '12px',
                    }}
                >
                    {children}
                </div>
            )}
        </div>
    )
}
