'use client'

import {useSelectedSport} from '@/context/SelectedSportContext'
import {leagues, LeagueType} from '@/types/league'
import {LeagueItem} from './LeagueItem'

export const Leagues = () => {
    const {selectedSport} = useSelectedSport()

    const getLeagues: LeagueType[] = leagues[selectedSport] ?? []

    return (
        <>
            {getLeagues.map((league) => (
                <LeagueItem key={league.id} league={league} />
            ))}
        </>
    )
}
