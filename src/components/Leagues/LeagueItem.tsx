'use client'

import {useSelectedLeague} from '@/context/SelectedLeagueContext'
import {LeagueType} from '@/types/league'
import cx from 'classnames'
import Image from 'next/image'
import React from 'react'
import styles from './LeagueItem.module.css'

interface LeagueItemProps {
    league: LeagueType
}

export const LeagueItem = React.memo(({league}: LeagueItemProps) => {
    const {selectedLeague, setSelectedLeague} = useSelectedLeague()
    const handleClick = (league: LeagueType) => setSelectedLeague(league.label)

    return (
        <a
            key={league.label}
            href={`/${league.sport}/${league.label}`}
            onClick={() => handleClick(league)}
        >
            <ul
                className={cx(styles.leagueItem, {
                    [styles.selected]: selectedLeague === league.label,
                })}
            >
                <li className={styles.leagueImage}>
                    <Image
                        src={`/leagues/${league.sport}/${league.label}.png`}
                        width={league.imageWidth ?? 24}
                        height={league.imageHeight ?? 24}
                        alt={league.displayName}
                    />
                </li>
                <li className={styles.leagueName}>{league.displayName}</li>
            </ul>
        </a>
    )
})
