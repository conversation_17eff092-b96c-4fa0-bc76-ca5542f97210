.leagueItem {
    display: flex;
    gap: 12px;
    align-items: center;

    height: 42px;
    margin: 0 16px;
    padding-left: 8px;
    border-radius: 8px;
}

.leagueItem:hover,
.leagueItem.selected {
    cursor: pointer;
    background: #1f2022;
}

.leagueImage {
    height: 24px;
}

.leagueName {
    font-family: 'Inter Tight', Arial, sans-serif;
    font-size: 16px;
    font-weight: 600;
    color: #fff;
}

@media screen and (width <= 960px) {
    .leagueName {
        display: none;
    }

    .leagueItem {
        justify-content: center;
        padding-left: 0;
    }
}
