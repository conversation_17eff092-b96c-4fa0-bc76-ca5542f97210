import {Highlight} from '@/types/highlights'
import {LeagueType} from '@/types/league'
import {
    GENERICKEYWORDS,
    HIGHLIGHTDESCRIPTION,
    HOMEDESCRIPTION,
    LEAGUEDESCRIPTION,
} from '@/utils/metadata'
import {leagueToDisplayName} from '../Shared/Util'

interface StructuredDataProps {
    type: 'organization' | 'highlight' | 'sport' | 'league' | 'breadcrumb'
    data?: {
        highlight?: Highlight
        league?: LeagueType
        sport?: string
        breadcrumbs?: Array<{name: string; url: string}>
    }
}

export default function StructuredData({type, data}: StructuredDataProps) {
    const generateOrganizationData = () => ({
        '@context': 'https://schema.org',
        '@type': 'Organization',
        name: 'Thepelota',
        url: 'https://thepelota.tv',
        logo: 'https://thepelota.tv/logo.jpg',
        description: HOMEDESCRIPTION,
        contactPoint: {
            '@type': 'ContactPoint',
            contactType: 'customer service',
            url: 'https://thepelota.tv',
        },
    })

    const generateHighlightData = () => {
        if (!data?.highlight) return null

        return {
            '@context': 'https://schema.org',
            '@type': 'VideoObject',
            name: ` ${data.highlight.name} - ${leagueToDisplayName(data.highlight.league)} ${data.highlight.season}`,
            description: HIGHLIGHTDESCRIPTION(
                data.highlight.name,
                data.highlight.league,
                data.highlight.season
            ),
            thumbnailUrl: data.highlight.thumbnail,
            uploadDate: data.highlight.created_at,
            datePublished: data.highlight.created_at,
            duration: data.highlight.duration,
            contentUrl: data.highlight.video_url,
            embedUrl: data.highlight.video_url,
            publisher: {
                '@type': 'Organization',
                name: 'YouTube',
                url: 'https://www.youtube.com',
            },
            genre: 'Sports',
            keywords: [data.highlight.league + ' highlights', ...GENERICKEYWORDS],
            inLanguage: 'en',
            encodingFormat: 'video/mp4',
            isAccessibleForFree: true,
            isFamilyFriendly: true,
            videoQuality: 'HD',
        }
    }

    const generateSportData = () => {
        if (!data?.sport) return null

        return {
            '@context': 'https://schema.org',
            '@type': 'WebPage',
            name: `${data.sport.charAt(0).toUpperCase() + data.sport.slice(1)} Highlights`,
            description: HOMEDESCRIPTION,
            url: `https://thepelota.tv/${data.sport}`,
            mainEntity: {
                '@type': 'ItemList',
                name: `${data.sport} Highlights`,
                description: `Collection of ${data.sport} highlights`,
            },
            breadcrumb: {
                '@type': 'BreadcrumbList',
                itemListElement: [
                    {
                        '@type': 'ListItem',
                        position: 1,
                        name: 'Home',
                        item: 'https://thepelota.tv',
                    },
                    {
                        '@type': 'ListItem',
                        position: 2,
                        name: data.sport.charAt(0).toUpperCase() + data.sport.slice(1),
                        item: `https://thepelota.tv/${data.sport}`,
                    },
                ],
            },
        }
    }

    const generateLeagueData = () => {
        if (!data?.league || !data?.sport) return null

        return {
            '@context': 'https://schema.org',
            '@type': 'WebPage',
            name: `${data.league.displayName} Highlights`,
            description: LEAGUEDESCRIPTION(data.league.displayName),
            url: `https://thepelota.tv/${data.sport}/${data.league.label}`,
            mainEntity: {
                '@type': 'ItemList',
                name: `${data.league.displayName} Highlights`,
                description: `Collection of ${data.league.displayName} highlights`,
            },
            breadcrumb: {
                '@type': 'BreadcrumbList',
                itemListElement: [
                    {
                        '@type': 'ListItem',
                        position: 1,
                        name: 'Home',
                        item: 'https://thepelota.tv',
                    },
                    {
                        '@type': 'ListItem',
                        position: 2,
                        name: data.sport.charAt(0).toUpperCase() + data.sport.slice(1),
                        item: `https://thepelota.tv/${data.sport}`,
                    },
                    {
                        '@type': 'ListItem',
                        position: 3,
                        name: data.league.displayName,
                        item: `https://thepelota.tv/${data.sport}/${data.league.label}`,
                    },
                ],
            },
        }
    }

    const generateBreadcrumbData = () => {
        if (!data?.breadcrumbs) return null

        return {
            '@context': 'https://schema.org',
            '@type': 'BreadcrumbList',
            itemListElement: data.breadcrumbs.map((crumb, index) => ({
                '@type': 'ListItem',
                position: index + 1,
                name: crumb.name,
                item: crumb.url,
            })),
        }
    }

    let structuredData = null

    switch (type) {
        case 'organization':
            structuredData = generateOrganizationData()
            break
        case 'highlight':
            structuredData = generateHighlightData()
            break
        case 'sport':
            structuredData = generateSportData()
            break
        case 'league':
            structuredData = generateLeagueData()
            break
        case 'breadcrumb':
            structuredData = generateBreadcrumbData()
            break
        default:
            return null
    }

    if (!structuredData) return null

    return (
        <script
            type='application/ld+json'
            dangerouslySetInnerHTML={{__html: JSON.stringify(structuredData)}}
        />
    )
}
