import Skeleton, {SkeletonTheme} from 'react-loading-skeleton'
import 'react-loading-skeleton/dist/skeleton.css'
import styles from './Highlight.module.css'

const HighlightItemLoading = () => {
    return (
        <div className={styles.highlightItem}>
            <Skeleton height={300} />
            <Skeleton height={28} />
        </div>
    )
}

const HighlightSplitItemLoading = () => {
    return (
        <div>
            <Skeleton width={92} height={42} />
            <div className={styles.highlightVideoIframe}>
                <HighlightItemLoading />
            </div>
        </div>
    )
}

export default function HighlightLoading() {
    return (
        <SkeletonTheme baseColor='#202020' highlightColor='#444' borderRadius={8}>
            <div className={styles.highlightContainer}>
                <HighlightSplitItemLoading />
            </div>
        </SkeletonTheme>
    )
}
