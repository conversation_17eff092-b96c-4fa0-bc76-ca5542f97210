.highlightContainer {
    overflow-y: auto;
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 64px;

    height: 100%;
    padding: 16px 24px;
}

.highlightBackButton {
    cursor: pointer;

    display: inline-block;

    padding: 12px 24px 12px 40px;
    border: none;
    border-radius: 16px;

    font-family: 'Inter Tight', Arial, sans-serif;
    font-size: 14px;
    font-weight: 600;
    color: #000;

    background-color: #fff;
    background-image: url('/other/back.svg');
    background-repeat: no-repeat;
    background-position: 17px center;
    background-size: 24px 24px;
}

.highlightBackButton:hover {
    filter: brightness(80%);
}

.highlightItem {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 16px;
}

.highlightVideoIframe {
    aspect-ratio: 16 / 9;
    width: 100%;
    max-width: 1460px;
}

.highlightTitle {
    font-family: 'Inter Tight', Arial, sans-serif;
    font-size: 18px;
    font-weight: 600;
    line-height: 28px;
    color: #fff;
}
