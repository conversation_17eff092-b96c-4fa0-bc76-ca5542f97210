.highlightsContainer {
    overflow-y: auto;
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 64px;

    height: 100%;
    padding: 16px 24px 128px;
}

.highlightsSplitItem {
    display: grid;
    grid-template-columns: repeat(auto-fill, 327px);
    gap: 24px;
    width: 100%;
}

.highlightsSplitDate {
    display: inline-block;

    padding: 14px;
    border-radius: 8px;

    font-family: 'Inter Tight', Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #ffff;

    background: #1f2022;
}

.highlightItem {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 16px;
}

.highlightItem:hover {
    cursor: pointer;
}

.highlightPreview {
    position: relative;

    overflow: hidden;
    display: grid;
    place-items: center;

    height: 184px;
    border-radius: 8px;

    background: #202020;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    filter: brightness(80%);
}

.highlightItem:hover .highlightPreview {
    filter: brightness(64%);
}

.videoDuration {
    position: absolute;
    right: 12px;
    bottom: 12px;

    padding: 4px 8px;
    border-radius: 16px;

    font-family: 'Inter Tight', Arial, sans-serif;
    font-size: 12px;
    font-weight: 600;
    color: white;

    background: #1f2022;
}

.highlightTitle {
    font-family: 'Inter Tight', Arial, sans-serif;
    font-size: 18px;
    font-weight: 600;
    line-height: 28px;
    color: #fff;
}

/* Pagination Styles */
.paginationContainer {
    display: flex;
    justify-content: center;
    margin-top: 32px;
    padding-bottom: 32px;
}

.pagination {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    justify-content: center;

    margin: 0;
    padding: 0;

    list-style: none;
}

.paginationPage,
.paginationPrevious,
.paginationNext,
.paginationBreak {
    display: flex;
    align-items: center;
    justify-content: center;
}

.paginationPageLink,
.paginationPreviousLink,
.paginationNextLink,
.paginationBreakLink {
    cursor: pointer;

    display: flex;
    align-items: center;
    justify-content: center;

    min-width: 40px;
    height: 40px;
    padding: 8px 12px;
    border: 1px solid #333;
    border-radius: 8px;

    font-family: 'Inter Tight', Arial, sans-serif;
    font-size: 14px;
    font-weight: 500;
    color: #fff;
    text-decoration: none;

    background: #1f2022;

    transition: all 0.2s ease;
}

.paginationPageLink:hover,
.paginationPreviousLink:hover,
.paginationNextLink:hover {
    border-color: #555;
    background: #333;
}

.paginationActive .paginationPageLink {
    border-color: #ff4b44;
    color: white;
    background: #ff4b44;
}

.paginationActive .paginationPageLink:hover {
    border-color: #e63e37;
    background: #e63e37;
}

.paginationDisabled .paginationPreviousLink,
.paginationDisabled .paginationNextLink {
    pointer-events: none;
    cursor: not-allowed;
    opacity: 0.5;
}

.paginationBreakLink {
    cursor: default;
    border: none;
    background: transparent;
}

/* Animations */
@keyframes fade-in {
    from {
        transform: translateY(10px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@media screen and (width <= 960px) {
    .highlightsContainer {
        gap: 32px;
        padding: 16px 16px 128px;
    }

    .highlightsSplitItem {
        grid-template-columns: repeat(auto-fill, 100%);
    }

    .highlightItem {
        gap: 8px;
    }

    .highlightPreview {
        background-size: contain;
    }

    .highlightTitle {
        font-size: 16px;
    }

    .highlightItem:hover .highlightPreview {
        filter: none;
    }

    .paginationContainer {
        margin-top: 24px;
        padding-bottom: 24px;
    }

    .pagination {
        gap: 4px;
    }

    .paginationPageLink,
    .paginationPreviousLink,
    .paginationNextLink {
        min-width: 36px;
        height: 36px;
        padding: 6px 10px;
        font-size: 12px;
    }
}
