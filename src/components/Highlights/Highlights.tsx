'use client'

import {usePagination} from '@/context/PaginationContext'
import {useSelectedLeague} from '@/context/SelectedLeagueContext'
import {useSelectedSport} from '@/context/SelectedSportContext'
import {Highlight, HighlightGroup} from '@/types/highlights'
import {calculatePaginationInfo} from '@/utils/pagination'
import {useQuery} from '@tanstack/react-query'
import Image from 'next/image'
import Link from 'next/link'
import ReactPaginate from 'react-paginate'
import Error from '../Shared/Error'
import LazyImage from '../Shared/LazyImage'
import styles from './Highlights.module.css'
import HighlightsLoading from './HighlightsLoading'

export default function Highlights() {
    const {selectedLeague} = useSelectedLeague()
    const {selectedSport} = useSelectedSport()
    const {currentPage, navigateToPage} = usePagination()

    const fetchAllHighlights = async () => {
        // Use cached API endpoint instead of direct Supabase query
        const response = await fetch(
            `/api/cached-highlights?sport=${selectedSport}&league=${selectedLeague}`
        )

        if (!response.ok) {
            const error = new globalThis.Error('Failed to fetch highlights')
            throw error
        }

        const result = await response.json()
        return result.highlights || []
    }

    const {
        data: allHighlights,
        isLoading,
        error,
    } = useQuery({
        queryKey: ['highlights', selectedSport, selectedLeague],
        queryFn: fetchAllHighlights,
        staleTime: 1000 * 60 * 5,
        enabled: !!selectedLeague,
    })

    // First, group all highlights by date
    const allHighlightGroups = allHighlights
        ? (allHighlights as Highlight[]).reduce<HighlightGroup[]>(
              (groups: HighlightGroup[], highlight: Highlight) => {
                  const highlightDate = new Date(highlight.created_at)
                  const today = new Date()
                  const yesterday = new Date(today)
                  yesterday.setDate(today.getDate() - 1)

                  let dateLabel: string

                  if (highlightDate.toDateString() === today.toDateString()) {
                      dateLabel = 'Today'
                  } else if (highlightDate.toDateString() === yesterday.toDateString()) {
                      dateLabel = 'Yesterday'
                  } else {
                      dateLabel = highlightDate.toLocaleString('default', {
                          month: 'long',
                          day: 'numeric',
                      })
                  }

                  const existingGroup = groups.find(
                      (group: HighlightGroup) => group.date === dateLabel
                  )

                  if (existingGroup) {
                      existingGroup.highlights.push(highlight)
                  } else {
                      groups.push({date: dateLabel, highlights: [highlight]})
                  }

                  return groups
              },
              []
          )
        : []

    // Calculate pagination based on groups, not individual highlights
    const GROUPS_PER_PAGE = 14 // Show 3 date groups per page
    const totalGroups = allHighlightGroups.length
    const startIndex = (currentPage - 1) * GROUPS_PER_PAGE
    const endIndex = startIndex + GROUPS_PER_PAGE

    // Get the groups for the current page
    const highlightGroups = allHighlightGroups.slice(startIndex, endIndex)

    const paginationInfo = calculatePaginationInfo(currentPage, totalGroups, GROUPS_PER_PAGE)

    if (isLoading) {
        return <HighlightsLoading />
    }

    if (error) {
        return (
            <Error
                title="Sorry, it's not you, it's us"
                message="We're having trouble loading the highlights right now. Please try again later."
            />
        )
    }

    if (highlightGroups.length === 0) {
        return (
            <Error
                title='No highlights found'
                message='It does not look like there are any highlights for this league.'
            />
        )
    }
    return (
        <div className={styles.highlightsContainer}>
            {highlightGroups.map((group: HighlightGroup) => (
                <div key={group.date}>
                    <h3 className={styles.highlightsSplitDate}>{group.date}</h3>
                    <section className={styles.highlightsSplitItem}>
                        {group.highlights.map((highlight: Highlight, index: number) => {
                            // Use Next.js Image for first 6 items (above-the-fold) for better LCP
                            const isAboveFold = index < 6
                            const globalIndex =
                                highlightGroups.findIndex((g) => g.date === group.date) * 10 + index

                            return (
                                <Link
                                    key={highlight.id}
                                    href={`/${highlight.sport}/${highlight.league}/${highlight.label}`}
                                >
                                    <ul className={styles.highlightItem}>
                                        <li>
                                            {isAboveFold && globalIndex < 6 ? (
                                                <div
                                                    className={styles.highlightPreview}
                                                    style={{position: 'relative'}}
                                                >
                                                    <Image
                                                        src={highlight.thumbnail}
                                                        alt={`${highlight.name} thumbnail`}
                                                        fill
                                                        sizes='(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
                                                        priority={globalIndex < 3}
                                                        style={{objectFit: 'cover'}}
                                                    />
                                                    <span
                                                        className={styles.videoDuration}
                                                        style={{
                                                            position: 'absolute',
                                                            bottom: '12px',
                                                            right: '12px',
                                                            zIndex: 2,
                                                        }}
                                                    >
                                                        {highlight.duration}
                                                    </span>
                                                </div>
                                            ) : (
                                                <LazyImage
                                                    src={highlight.thumbnail}
                                                    alt={`${highlight.name} thumbnail`}
                                                    className={styles.highlightPreview}
                                                    loadingDelay={index * 50} // Reduced delay
                                                    fallbackSrc={`https://img.youtube.com/vi/${highlight.video_url.split('v=')[1]}/hqdefault.jpg`}
                                                >
                                                    <span className={styles.videoDuration}>
                                                        {highlight.duration}
                                                    </span>
                                                </LazyImage>
                                            )}
                                        </li>
                                        <li className={styles.highlightTitle}>
                                            <h2>{highlight.name}</h2>
                                        </li>
                                    </ul>
                                </Link>
                            )
                        })}
                    </section>
                </div>
            ))}

            {paginationInfo && paginationInfo.totalPages > 1 && (
                <div className={styles.paginationContainer}>
                    <ReactPaginate
                        pageCount={paginationInfo.totalPages}
                        pageRangeDisplayed={5}
                        marginPagesDisplayed={2}
                        onPageChange={(event) => navigateToPage(event.selected + 1)}
                        forcePage={currentPage - 1}
                        containerClassName={styles.pagination}
                        pageClassName={styles.paginationPage}
                        pageLinkClassName={styles.paginationPageLink}
                        activeClassName={styles.paginationActive}
                        previousClassName={styles.paginationPrevious}
                        nextClassName={styles.paginationNext}
                        previousLinkClassName={styles.paginationPreviousLink}
                        nextLinkClassName={styles.paginationNextLink}
                        disabledClassName={styles.paginationDisabled}
                        breakClassName={styles.paginationBreak}
                        breakLinkClassName={styles.paginationBreakLink}
                        previousLabel='Previous'
                        nextLabel='Next'
                    />
                </div>
            )}
        </div>
    )
}
