import Skeleton, {SkeletonTheme} from 'react-loading-skeleton'
import 'react-loading-skeleton/dist/skeleton.css'
import styles from './Highlights.module.css'

const HighlightsItemLoading = ({delay = 0}: {delay?: number}) => {
    return (
        <div
            className={styles.highlightItem}
            style={{
                animationDelay: `${delay}ms`,
                animation: 'fade-in 0.3s ease-in-out forwards',
                //opacity: 0,
            }}
        >
            <div className={styles.highlightPreview}>
                <Skeleton height={184} />
            </div>
            <Skeleton height={28} />
        </div>
    )
}

const HighlightsSplitItemLoading = ({groupIndex = 0}: {groupIndex?: number}) => {
    const baseDelay = groupIndex * 200 // Stagger groups

    return (
        <div>
            <Skeleton width={100} height={42} />
            <div className={styles.highlightsSplitItem}>
                <HighlightsItemLoading delay={baseDelay} />
                <HighlightsItemLoading delay={baseDelay + 100} />
                <HighlightsItemLoading delay={baseDelay + 200} />
                <HighlightsItemLoading delay={baseDelay + 300} />
                <HighlightsItemLoading delay={baseDelay + 400} />
                <HighlightsItemLoading delay={baseDelay + 500} />
            </div>
        </div>
    )
}

export default function HighlightsLoading() {
    return (
        <SkeletonTheme baseColor='#202020' highlightColor='#444' borderRadius={8}>
            <div className={styles.highlightsContainer}>
                <HighlightsSplitItemLoading groupIndex={0} />
                <HighlightsSplitItemLoading groupIndex={1} />
                <HighlightsSplitItemLoading groupIndex={2} />
            </div>
        </SkeletonTheme>
    )
}
