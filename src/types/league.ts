export interface LeagueType {
    id: number
    sport: string
    label: string
    displayName: string
    imageWidth?: number
    imageHeight?: number
}

export type LeaguesType<T extends string> = Record<T, LeagueType[]>

const soccerLeagues: LeagueType[] = [
    {
        id: 1,
        sport: 'soccer',
        label: 'premier-league',
        displayName: 'Premier League',
        imageWidth: 19,
    },
    {
        id: 2,
        sport: 'soccer',
        label: 'laliga',
        displayName: 'LaLiga',
    },
    {
        id: 3,
        sport: 'soccer',
        label: 'bundesliga',
        displayName: 'Bundesliga',
    },
    {
        id: 4,
        sport: 'soccer',
        label: 'serie-a',
        displayName: 'Serie A',
    },
    {
        id: 5,
        sport: 'soccer',
        label: 'ligue-1',
        displayName: 'Ligue 1',
    },
    {
        id: 6,
        sport: 'soccer',
        label: 'mls',
        displayName: 'MLS',
    },
    {
        id: 7,
        sport: 'soccer',
        label: 'champions-league',
        displayName: 'Champions League',
    },
    {
        id: 8,
        sport: 'soccer',
        label: 'europa-league',
        displayName: 'Europa League',
    },
    {
        id: 9,
        sport: 'soccer',
        label: 'conference-league',
        displayName: 'Conference League',
    },
    {
        id: 10,
        sport: 'soccer',
        label: 'club-world-cup',
        displayName: 'Club World Cup',
    },
]

/*
const basketballLeagues: LeagueType[] = [
    {
        id: 10,
        sport: 'basketball',
        label: 'nba',
        displayName: 'NBA',
    },
    {
        id: 11,
        sport: 'basketball',
        label: 'euroleague',
        displayName: 'EuroLeague',
    },
]

const cricketLeagues: LeagueType[] = [
    {
        id: 12,
        sport: 'cricket',
        label: 'conference-league',
        displayName: 'Conference League',
    },
]

const golfLeagues: LeagueType[] = [
    {
        id: 13,
        sport: 'golf',
        label: 'conference-league',
        displayName: 'Conference League',
    },
]

const hockeyLeagues: LeagueType[] = [
    {
        id: 14,
        sport: 'hockey',
        label: 'conference-league',
        displayName: 'Conference League',
    },
]

const rugbyLeagues: LeagueType[] = [
    {
        id: 15,
        sport: 'rugby',
        label: 'conference-league',
        displayName: 'Conference League',
    },
]

const tennisLeagues: LeagueType[] = [
    {
        id: 16,
        sport: 'tennis',
        label: 'conference-league',
        displayName: 'Conference League',
    },
]

const baseballLeagues: LeagueType[] = [
    {
        id: 17,
        sport: 'baseball',
        label: 'mlb',
        displayName: 'MLB',
    },
    {
        id: 18,
        sport: 'baseball',
        label: 'lmb',
        displayName: 'LMB',
    },
]
*/

export const leagues: LeaguesType<string> = {
    soccer: soccerLeagues,
    /*
    basketball: basketballLeagues,
    cricket: cricketLeagues,
    golf: golfLeagues,
    hockey: hockeyLeagues,
    rugby: rugbyLeagues,
    tennis: tennisLeagues,
    baseball: baseballLeagues,
    */
}
