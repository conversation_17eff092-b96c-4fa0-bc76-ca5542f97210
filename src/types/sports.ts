export interface Sport {
    id: number
    name: string
    label: string
}

export const sports: Sport[] = [
    {
        id: 1,
        name: 'Soccer',
        label: 'soccer',
    },
    /*
    {
        id: 2,
        name: 'Cricket',
        label: 'cricket',
    },
    {
        id: 3,
        name: 'Hockey',
        label: 'hockey',
    },
    {
        id: 4,
        name: 'Tennis',
        label: 'tennis',
    },
    {
        id: 5,
        name: 'Basketball',
        label: 'basketball',
    },
    {
        id: 6,
        name: 'Baseball',
        label: 'baseball',
    },
    {
        id: 7,
        name: 'Rugby',
        label: 'rugby',
    },
    {
        id: 8,
        name: 'Golf',
        label: 'golf',
    },
    */
]
