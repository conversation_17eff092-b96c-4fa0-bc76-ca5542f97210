import Highlights from '@/components/Highlights/Highlights'
import HighlightsLoading from '@/components/Highlights/HighlightsLoading'
import {Leagues} from '@/components/Leagues/Leagues'
import {metadataForHomePage} from '@/utils/metadata'
import {Metadata} from 'next'
import {Suspense} from 'react'
import styles from './page.module.css'

export const metadata: Metadata = {
    ...metadataForHomePage,
}

function HomeComponent() {
    return (
        <div className={styles.pageContainer}>
            <aside>
                <Leagues />
            </aside>
            <Suspense fallback={<HighlightsLoading />}>
                <Highlights />
            </Suspense>
        </div>
    )
}

export default async function Page() {
    return <HomeComponent />
}
