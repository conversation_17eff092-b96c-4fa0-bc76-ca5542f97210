import Highlights from '@/components/Highlights/Highlights'
import HighlightsLoading from '@/components/Highlights/HighlightsLoading'
import {Leagues} from '@/components/Leagues/Leagues'
import StructuredData from '@/components/SEO/StructuredData'
import {metadataForHomePage} from '@/utils/metadata'
import {Metadata} from 'next'
import {Suspense} from 'react'
import styles from '../page.module.css'

type Props = {
    params: Promise<{
        sport: string
    }>
    searchParams: Promise<{[key: string]: string | string[] | undefined}>
}

export const metadata: Metadata = {
    ...metadataForHomePage,
}

export default async function Page({params}: Props) {
    const {sport} = await params

    return (
        <>
            <StructuredData type='sport' data={{sport}} />
            <div className={styles.pageContainer}>
                <aside>
                    <Leagues />
                </aside>
                <Suspense fallback={<HighlightsLoading />}>
                    <Highlights />
                </Suspense>
            </div>
        </>
    )
}
