import Highlights from '@/components/Highlights/Highlights'
import HighlightsLoading from '@/components/Highlights/HighlightsLoading'
import {Leagues} from '@/components/Leagues/Leagues'
import StructuredData from '@/components/SEO/StructuredData'
import {leagues} from '@/types/league'
import {LEAGUEDESCRIPTION, metadataForSportAndLeaguePages} from '@/utils/metadata'
import {Metadata} from 'next'
import {Suspense} from 'react'
import styles from '../../page.module.css'

export async function generateMetadata({
    params,
}: {
    params: Promise<{sport: string; league: string}>
}): Promise<Metadata> {
    const {sport, league} = await params
    const sportLeagues = leagues[sport] || []
    const leagueInfo = sportLeagues.find((l) => l.label === league)
    const leagueName = leagueInfo?.displayName || league

    return metadataForSportAndLeaguePages({
        title: `${leagueName} Highlights`,
        description: LEAGUEDESCRIPTION(leagueName),
        url: `https://thepelota.tv/${sport}/${league}`,
    })
}

export default async function Page({params}: {params: Promise<{sport: string; league: string}>}) {
    const {sport, league} = await params
    const sportLeagues = leagues[sport] || []
    const leagueInfo = sportLeagues.find((l) => l.label === league)

    return (
        <>
            <StructuredData type='league' data={{league: leagueInfo, sport}} />
            <div className={styles.pageContainer}>
                <aside>
                    <Leagues />
                </aside>
                <Suspense fallback={<HighlightsLoading />}>
                    <Highlights />
                </Suspense>
            </div>
        </>
    )
}
