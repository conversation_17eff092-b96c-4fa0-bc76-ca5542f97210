import Highlights from '@/components/Highlights/Highlights'
import HighlightsLoading from '@/components/Highlights/HighlightsLoading'
import {Leagues} from '@/components/Leagues/Leagues'
import {HOMEDESCRIPTION, metadataForSportAndLeaguePages} from '@/utils/metadata'
import {Metadata} from 'next'
import {Suspense} from 'react'
import styles from '../../../page.module.css'

type Props = {
    params: Promise<{
        sport: string
        page: string
    }>
    searchParams: Promise<{[key: string]: string | string[] | undefined}>
}

export async function generateMetadata({params}: Props): Promise<Metadata> {
    const {sport, page} = await params
    const sportName = sport.charAt(0).toUpperCase() + sport.slice(1)

    return metadataForSportAndLeaguePages({
        title: `${sportName} Highlights - Page ${page}`,
        description: HOMEDESCRIPTION,
        url: `https://thepelota.tv/${sport}/page/${page}`,
    })
}

export default function Page() {
    return (
        <div className={styles.pageContainer}>
            <aside>
                <Leagues />
            </aside>
            <Suspense fallback={<HighlightsLoading />}>
                <Highlights />
            </Suspense>
        </div>
    )
}
