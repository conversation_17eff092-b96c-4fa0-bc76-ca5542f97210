import {leagues} from '@/types/league'
import {sports} from '@/types/sports'
import {supabase} from '@/utils/supabaseClient'
import {MetadataRoute} from 'next'

const SITE_URL = 'https://thepelota.tv'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
    try {
        // For sitemap generation, always fetch directly from database
        // Caching is not beneficial here since sitemaps are generated infrequently
        const {data: highlights} = await supabase
            .from('highlights')
            .select('label, sport, league, created_at')
            .order('created_at', {ascending: false})

        const sitemapEntries: MetadataRoute.Sitemap = []
        const currentDate = new Date()

        // Homepage
        sitemapEntries.push({
            url: SITE_URL,
            lastModified: currentDate,
            changeFrequency: 'daily',
            priority: 1.0,
        })

        // Sport pages
        sports.forEach((sport) => {
            sitemapEntries.push({
                url: `${SITE_URL}/${sport.label}`,
                lastModified: currentDate,
                changeFrequency: 'daily',
                priority: 0.8,
            })

            // League pages for each sport
            const sportLeagues = leagues[sport.label] || []
            sportLeagues.forEach((league) => {
                sitemapEntries.push({
                    url: `${SITE_URL}/${sport.label}/${league.label}`,
                    lastModified: currentDate,
                    changeFrequency: 'daily',
                    priority: 0.7,
                })
            })
        })

        // Highlight pages
        if (highlights) {
            highlights.forEach((highlight: any) => {
                sitemapEntries.push({
                    url: `${SITE_URL}/${highlight.sport}/${highlight.league}/${highlight.label}`,
                    lastModified: new Date(highlight.created_at),
                    changeFrequency: 'weekly',
                    priority: 0.6,
                })
            })

            // Add pagination pages (estimate based on highlights count)
            const highlightsPerPage = 20
            const totalPages = Math.ceil(highlights.length / highlightsPerPage)

            // Homepage pagination
            for (let page = 2; page <= Math.min(totalPages, 50); page++) {
                sitemapEntries.push({
                    url: `${SITE_URL}/soccer/page/${page}`,
                    lastModified: currentDate,
                    changeFrequency: 'daily',
                    priority: 0.5,
                })
            }

            // League-specific pagination
            sports.forEach((sport) => {
                const sportLeagues = leagues[sport.label] || []
                sportLeagues.forEach((league) => {
                    const leagueHighlights = highlights.filter(
                        (h: any) => h.sport === sport.label && h.league === league.label
                    )
                    const leaguePages = Math.ceil(leagueHighlights.length / highlightsPerPage)

                    for (let page = 2; page <= Math.min(leaguePages, 20); page++) {
                        sitemapEntries.push({
                            url: `${SITE_URL}/${sport.label}/${league.label}/page/${page}`,
                            lastModified: currentDate,
                            changeFrequency: 'daily',
                            priority: 0.4,
                        })
                    }
                })
            })
        }

        return sitemapEntries
    } catch (error) {
        console.error('Error generating sitemap:', error)
        // Return basic sitemap if there's an error
        return [
            {
                url: SITE_URL,
                lastModified: new Date(),
                changeFrequency: 'daily',
                priority: 1.0,
            },
        ]
    }
}
