a,
abbr,
acronym,
address,
applet,
article,
nav,
audio,
b,
big,
blockquote,
body,
canvas,
caption,
center,
cite,
code,
dd,
del,
details,
dfn,
div,
dl,
dt,
em,
embed,
fieldset,
figcaption,
figure,
footer,
form,
h1,
h2,
h3,
h4,
h5,
h6,
header,
hgroup,
html,
i,
iframe,
img,
ins,
kbd,
label,
legend,
li,
mark,
menu,
object,
ol,
output,
p,
pre,
q,
ruby,
s,
samp,
section,
small,
span,
strike,
strong,
sub,
summary,
sup,
table,
tbody,
td,
tfoot,
th,
thead,
time,
tr,
tt,
u,
ul,
var,
video {
    margin: 0;
    padding: 0;
    border: 0;

    font: inherit;
    vertical-align: baseline;
}

a {
    color: inherit;
    text-decoration: none;
}

article,
nav,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
section {
    display: block;
}

body {
    overflow: hidden;
    height: 100vh;
    line-height: 1;
    background: #141414;
}

ol,
ul {
    list-style: none;
}

blockquote,
q {
    quotes: none;
}

blockquote::after,
blockquote::before,
q::after,
q::before {
    content: none;
}

table {
    border-spacing: 0;
    border-collapse: collapse;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;

    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizelegibility;
    overflow-wrap: break-word;

    -webkit-text-stroke: 1px transparent;
}

header {
    display: flex;
    align-items: center;

    padding: 12px 24px 4px;
    border-bottom: 1px solid #1f2022;

    background: #ff4b44 url('/other/header.png') no-repeat right;
}

header h1 {
    font-family: Teko, Arial, sans-serif;
    font-size: 36px;
    font-weight: 500;
    color: #fff;
    text-transform: uppercase;
}

main {
    display: flex;
    height: calc(100vh - 53px);
}

nav {
    position: sticky;
    top: 0;

    display: none;
    justify-content: center;

    min-width: 90px;
    padding: 16px 0;
    border-right: 1px solid #1f2022;
}

section {
    width: 100%;
}

@media screen and (width <= 960px) {
    nav {
        display: none;
    }
}
