import {HighlightsCache} from '@/utils/cache'
import {NextRequest, NextResponse} from 'next/server'

export async function GET(request: NextRequest) {
    try {
        const {searchParams} = new URL(request.url)
        const action = searchParams.get('action')

        switch (action) {
            case 'clear-all':
                await HighlightsCache.invalidateAll()
                return NextResponse.json({
                    success: true,
                    message: 'All cache cleared',
                    timestamp: new Date().toISOString(),
                })

            case 'clear-league':
                const league = searchParams.get('league')
                if (!league) {
                    return NextResponse.json(
                        {error: 'League parameter required for clear-league action'},
                        {status: 400}
                    )
                }
                await HighlightsCache.invalidateForLeague(league)
                return NextResponse.json({
                    success: true,
                    message: `Cache cleared for league: ${league}`,
                    timestamp: new Date().toISOString(),
                })

            default:
                return NextResponse.json(
                    {
                        error: 'Invalid action. Available actions: clear-all, clear-league',
                        availableActions: ['clear-all', 'clear-league'],
                    },
                    {status: 400}
                )
        }
    } catch (error) {
        console.error('Cache admin error:', error)
        return NextResponse.json({error: 'Internal server error'}, {status: 500})
    }
}
