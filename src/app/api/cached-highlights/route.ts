import {HighlightsCache} from '@/utils/cache'
import {supabase} from '@/utils/supabaseClient'
import {NextRequest, NextResponse} from 'next/server'

export async function GET(request: NextRequest) {
    try {
        const {searchParams} = new URL(request.url)
        const sport = searchParams.get('sport')
        const league = searchParams.get('league') || 'all'

        if (!sport) {
            return NextResponse.json({error: 'Sport parameter is required'}, {status: 400})
        }

        // Try to get from cache first
        const cachedHighlights = await HighlightsCache.getHighlights(sport, league)

        if (cachedHighlights) {
            console.log(`Cache HIT for ${sport}:${league}`)
            return NextResponse.json(
                {
                    highlights: cachedHighlights,
                    cached: true,
                    timestamp: new Date().toISOString(),
                },
                {
                    headers: {
                        'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600',
                        'X-Cache': 'HIT',
                    },
                }
            )
        }

        console.log(`Cache MISS for ${sport}:${league} - fetching from database`)

        // Cache miss - fetch from database
        let query = supabase
            .from('highlights')
            .select(
                'id, name, label, duration, thumbnail, video_url, sport, season, created_at, league, country'
            )
            .eq('sport', sport)
            .order('created_at', {ascending: false})

        if (league !== 'all') {
            query = query.eq('league', league)
        }

        const {data: highlights, error} = await query

        if (error) {
            console.error('Database error:', error)
            return NextResponse.json({error: 'Failed to fetch highlights'}, {status: 500})
        }

        const highlightsData = highlights || []

        // Cache the results
        await HighlightsCache.setHighlights(sport, league, highlightsData)

        return NextResponse.json(
            {
                highlights: highlightsData,
                cached: false,
                timestamp: new Date().toISOString(),
            },
            {
                headers: {
                    'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600',
                    'X-Cache': 'MISS',
                },
            }
        )
    } catch (error) {
        console.error('API error:', error)
        return NextResponse.json({error: 'Internal server error'}, {status: 500})
    }
}
