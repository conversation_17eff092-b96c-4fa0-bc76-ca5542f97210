.pageContainer {
    display: flex;
    height: 100%;
}

.page<PERSON><PERSON>r aside {
    position: sticky;
    top: 0;

    display: flex;
    flex-direction: column;
    gap: 16px;

    min-width: 260px;
    padding: 16px 0;
    border-right: 1px solid #1f2022;

    color: #fff;
}

.pageContainerContent {
    display: flex;
    flex-direction: column;
    gap: 16px;

    padding: 16px 24px 128px;

    color: #fff;
}

.pageContainerContent ol li {
    margin-bottom: 24px;
}

.pageContainerContent h2 {
    font-family: 'Inter Tight', Arial, sans-serif;
    font-size: 32px;
    font-weight: 600;
    line-height: 28px;
    color: #fff;
}

.pageContainerContent h3 {
    font-family: 'Inter Tight', Arial, sans-serif;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
}

.pageContainerContent p {
    font-family: 'Inter Tight', Arial, sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 24px;
}

@media screen and (width <= 960px) {
    .page<PERSON>ontainer aside {
        min-width: 57px;
    }
}
