import type {Metadata} from 'next'

const OGIMAGE = 'https://thepelota.tv/og-image.jpg'

export function metadataForSportAndLeaguePages({
    title,
    description,
    url,
}: {
    title: string
    description: string
    url: string
}): Metadata {
    return {
        title: title + ' - Thepel<PERSON>',
        description,
        applicationName: 'Thepelota',
        robots: {
            index: true,
            follow: true,
        },
        alternates: {
            canonical: url,
        },
        openGraph: {
            title: title + ' - Thepelota',
            description,
            url,
            siteName: 'Thepelota',
            type: 'website',
            locale: 'en_US',
            images: [
                {
                    url: OGIMAGE,
                    width: 1200,
                    height: 630,
                    alt: title + ' - Thepelota',
                },
            ],
        },
        twitter: {
            card: 'summary_large_image',
            title: title + ' - Thepel<PERSON>',
            description,
            images: [OGIMAGE],
        },
    }
}

const HOMETITLE = 'Thepelota – Soccer & Football Highlights, Goals & Key Plays'
export const HOMEDESCRIPTION =
    'Watch the latest soccer and football highlights and goals from the Premier League, Champions League, LaLiga, Serie A, Bundesliga, and more.'
const HOMELINK = 'https://thepelota.tv'
export const GENERICKEYWORDS = [
    'football highlights',
    'soccer highlights',
    'sports highlights',
    'match highlights',
    "today's football highlights",
    "today's soccer highlights",
    'watch football highlights',
    'free football highlights',
    'hd soccer highlights',
    'latest match highlights',
    'football goals and highlights',
    'football highlights video',
    'all goals today',
    'match of the day',
    'football goals today',
    'goal of the week',
    'weekend football highlights',
    'football highlight videos',
    'watch soccer goals',
    "today's match goals",
    'football highlights online',
]

const metadataForHomeLayoutPages = {
    description: HOMEDESCRIPTION,
    applicationName: 'Thepelota',
    robots: {
        index: true,
        follow: true,
    },
    alternates: {
        canonical: HOMELINK,
    },
    openGraph: {
        title: HOMETITLE,
        description: HOMEDESCRIPTION,
        url: HOMELINK,
        siteName: 'Thepelota',
        type: 'website',
        locale: 'en_US',
        images: [
            {
                url: OGIMAGE,
                width: 1200,
                height: 630,
                alt: HOMETITLE,
            },
        ],
    },
    twitter: {
        card: 'summary_large_image',
        title: HOMETITLE,
        description: HOMEDESCRIPTION,
        images: [OGIMAGE],
    },
}

export const metadataForHomePage = {
    title: HOMETITLE,
    ...metadataForHomeLayoutPages,
}

export const metadataForLayoutPage = {
    title: {
        template: '%s',
        default: 'Thepelota',
    },
    ...metadataForHomeLayoutPages,
}

export const LEAGUEDESCRIPTION = (leagueName: string) =>
    `Watch the latest ${leagueName} soccer and football highlights, goals, and key plays from every match — updated daily with the best action from top teams.`

export const HIGHLIGHTDESCRIPTION = (name: string, league: string, season: string) =>
    `Watch ${name} match highlights from ${league} ${season}. All goals, big moments, and key plays.`
