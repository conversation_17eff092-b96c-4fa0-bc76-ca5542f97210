import {Highlight} from '@/types/highlights'
import {Redis} from '@upstash/redis'

// Initialize Redis client
const redis = new Redis({
    url: process.env.KV_REST_API_URL!,
    token: process.env.KV_REST_API_TOKEN!,
})

// Cache key generators
export const getCacheKey = {
    highlights: (sport: string, league: string) => `highlights:${sport}:${league}`,
    allHighlights: (sport: string) => `highlights:${sport}:all`,
    highlight: (label: string) => `highlight:${label}`,
    sitemap: () => 'sitemap:highlights',
}

// Cache TTL in seconds (5 minutes to match cron frequency)
const CACHE_TTL = 300

export class HighlightsCache {
    // Get cached highlights for a sport/league combination
    static async getHighlights(sport: string, league: string): Promise<Highlight[] | null> {
        try {
            const key = getCacheKey.highlights(sport, league)
            const cached = await redis.get(key)
            return cached as Highlight[] | null
        } catch (error) {
            console.error('Cache get error:', error)
            return null
        }
    }

    // Set cached highlights for a sport/league combination
    static async setHighlights(
        sport: string,
        league: string,
        highlights: Highlight[]
    ): Promise<void> {
        try {
            const key = getCacheKey.highlights(sport, league)
            await redis.setex(key, CACHE_TTL, JSON.stringify(highlights))
        } catch (error) {
            console.error('Cache set error:', error)
        }
    }

    // Get cached single highlight
    static async getHighlight(label: string): Promise<Highlight[] | null> {
        try {
            const key = getCacheKey.highlight(label)
            const cached = await redis.get(key)
            return cached as Highlight[] | null
        } catch (error) {
            console.error('Cache get error:', error)
            return null
        }
    }

    // Set cached single highlight
    static async setHighlight(label: string, highlight: Highlight[]): Promise<void> {
        try {
            const key = getCacheKey.highlight(label)
            await redis.setex(key, CACHE_TTL, JSON.stringify(highlight))
        } catch (error) {
            console.error('Cache set error:', error)
        }
    }

    // Get cached sitemap data
    static async getSitemapData(): Promise<any[] | null> {
        try {
            const key = getCacheKey.sitemap()
            const cached = await redis.get(key)
            return cached as any[] | null
        } catch (error) {
            console.error('Cache get error:', error)
            return null
        }
    }

    // Set cached sitemap data
    static async setSitemapData(data: any[]): Promise<void> {
        try {
            const key = getCacheKey.sitemap()
            await redis.setex(key, CACHE_TTL, JSON.stringify(data))
        } catch (error) {
            console.error('Cache set error:', error)
        }
    }

    // Invalidate cache when new highlights are added
    static async invalidateForLeague(league: string): Promise<void> {
        try {
            // Get all possible cache keys that might be affected
            const patterns = [
                `highlights:*:${league}`,
                `highlights:*:all`, // "all" leagues cache needs to be invalidated too
                `sitemap:*`,
            ]

            // Get all keys matching the patterns
            const keysToDelete: string[] = []

            for (const pattern of patterns) {
                const keys = await redis.keys(pattern)
                keysToDelete.push(...keys)
            }

            // Delete all affected cache entries
            if (keysToDelete.length > 0) {
                await redis.del(...keysToDelete)
                console.log(
                    `Cache invalidated for league ${league}: ${keysToDelete.length} keys deleted`
                )
            }
        } catch (error) {
            console.error('Cache invalidation error:', error)
        }
    }

    // Invalidate all highlights cache (nuclear option)
    static async invalidateAll(): Promise<void> {
        try {
            const keys = await redis.keys('highlights:*')
            const sitemapKeys = await redis.keys('sitemap:*')
            const allKeys = [...keys, ...sitemapKeys]

            if (allKeys.length > 0) {
                await redis.del(...allKeys)
                console.log(`All highlights cache invalidated: ${allKeys.length} keys deleted`)
            }
        } catch (error) {
            console.error('Cache invalidation error:', error)
        }
    }
}
