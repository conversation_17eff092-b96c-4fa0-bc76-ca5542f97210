export interface PaginationParams {
    page: number
    limit: number
    offset: number
}

export interface PaginationInfo {
    currentPage: number
    totalPages: number
    totalItems: number
    hasNextPage: boolean
    hasPreviousPage: boolean
}

export const HIGHLIGHTS_PER_PAGE = 20

/**
 * Calculate pagination parameters for database queries
 */
export function calculatePaginationParams(
    page: number,
    limit: number = HIGHLIGHTS_PER_PAGE
): PaginationParams {
    const currentPage = Math.max(1, page)
    const offset = (currentPage - 1) * limit

    return {
        page: currentPage,
        limit,
        offset,
    }
}

/**
 * Calculate pagination info for UI components
 */
export function calculatePaginationInfo(
    currentPage: number,
    totalItems: number,
    limit: number = HIGHLIGHTS_PER_PAGE
): PaginationInfo {
    const totalPages = Math.ceil(totalItems / limit)

    return {
        currentPage: Math.max(1, currentPage),
        totalPages,
        totalItems,
        hasNextPage: currentPage < totalPages,
        hasPreviousPage: currentPage > 1,
    }
}

/**
 * Generate URL for homepage pagination
 */
export function generateHomepagePageUrl(sport: string, page: number): string {
    if (page <= 1) {
        return `/${sport}`
    }
    return `/${sport}/page/${page}`
}

/**
 * Generate URL for league page pagination
 */
export function generateLeaguePageUrl(sport: string, league: string, page: number): string {
    if (page <= 1) {
        return `/${sport}/${league}`
    }
    return `/${sport}/${league}/page/${page}`
}

/**
 * Extract page number from URL pathname
 */
export function extractPageFromPath(pathname: string): number {
    const pageMatch = pathname.match(/\/page\/(\d+)$/)
    return pageMatch ? parseInt(pageMatch[1], 10) : 1
}

/**
 * Check if current path is a paginated URL
 */
export function isPaginatedPath(pathname: string): boolean {
    return /\/page\/\d+$/.test(pathname)
}

/**
 * Get base path without pagination
 */
export function getBasePath(pathname: string): string {
    return pathname.replace(/\/page\/\d+$/, '')
}

/**
 * Validate page number
 */
export function validatePageNumber(page: number, totalPages: number): number {
    if (page < 1) return 1
    if (page > totalPages && totalPages > 0) return totalPages
    return page
}
