'use client'

import {usePathname, useRouter} from 'next/navigation'
import React, {createContext, ReactNode, useContext, useEffect, useState} from 'react'
import {useSelectedLeague} from './SelectedLeagueContext'
import {useSelectedSport} from './SelectedSportContext'

interface SelectedHighlightContextType {
    selectedHighlight: string
    setSelectedHighlight: (highlight: string) => void
}

const SelectedHighlightContext = createContext<SelectedHighlightContextType | undefined>(undefined)

export const SelectedHighlightProvider: React.FC<{children: ReactNode}> = ({children}) => {
    const router = useRouter()
    const pathname = usePathname()
    const {selectedSport} = useSelectedSport()
    const {selectedLeague} = useSelectedLeague()

    const getHighlightFromPath = () => {
        const pathParts = pathname?.split('/').filter(Boolean)
        return pathParts?.[2]
    }

    const [selectedHighlight, setSelectedHighlight] = useState<string>(getHighlightFromPath() || '')

    useEffect(() => {
        const highlightFromPath = getHighlightFromPath()
        if (highlightFromPath !== selectedHighlight) {
            setSelectedHighlight(highlightFromPath || '')
        }
    }, [pathname, selectedHighlight])

    const handleSetSelectedHighlight = (highlight: string) => {
        setSelectedHighlight(highlight)
        router.push(`/${selectedSport}/${selectedLeague}/${highlight}`)
    }

    const contextValue = React.useMemo(
        () => ({selectedHighlight, setSelectedHighlight: handleSetSelectedHighlight}),
        [selectedHighlight]
    )

    return (
        <SelectedHighlightContext.Provider value={contextValue}>
            {children}
        </SelectedHighlightContext.Provider>
    )
}

export const useSelectedHighlight = () => {
    const context = useContext(SelectedHighlightContext)
    if (!context) {
        throw new Error('useSelectedHighlight must be used within a SelectedHighlightProvider')
    }
    return context
}
