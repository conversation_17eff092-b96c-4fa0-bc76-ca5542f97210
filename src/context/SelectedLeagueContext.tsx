'use client'

import {usePathname, useRouter} from 'next/navigation'
import React, {createContext, ReactNode, useContext, useEffect, useState} from 'react'
import {useSelectedSport} from './SelectedSportContext'

interface SelectedLeagueContextType {
    selectedLeague: string
    setSelectedLeague: (league: string) => void
}

const SelectedLeagueContext = createContext<SelectedLeagueContextType | undefined>(undefined)

export const SelectedLeagueProvider: React.FC<{children: ReactNode}> = ({children}) => {
    const router = useRouter()
    const pathname = usePathname()
    const {selectedSport} = useSelectedSport()

    const getLeagueFromPath = () => {
        const pathParts = pathname?.split('/').filter(Boolean)

        // Handle pagination URLs
        // For /soccer/page/2 -> should return 'all' (homepage pagination)
        // For /soccer/premier-league/page/2 -> should return 'premier-league' (league pagination)
        if (pathParts?.[1] === 'page') {
            return 'all'
        }

        // For /soccer/premier-league/page/2, we want pathParts[1] which is 'premier-league'
        // For /soccer/premier-league, we want pathParts[1] which is 'premier-league'
        // For /soccer, we want 'all'
        return pathParts?.[1] || 'all'
    }

    const [selectedLeague, setSelectedLeague] = useState<string>(getLeagueFromPath())

    useEffect(() => {
        const leagueFromPath = getLeagueFromPath()
        if (leagueFromPath !== selectedLeague) {
            setSelectedLeague(leagueFromPath)
        }
    }, [pathname, selectedLeague])

    const handleSetSelectedLeague = (league: string) => {
        setSelectedLeague(league)
        router.push(`/${selectedSport}/${league}`)
    }

    const contextValue = React.useMemo(
        () => ({selectedLeague, setSelectedLeague: handleSetSelectedLeague}),
        [selectedLeague]
    )

    return (
        <SelectedLeagueContext.Provider value={contextValue}>
            {children}
        </SelectedLeagueContext.Provider>
    )
}

export const useSelectedLeague = () => {
    const context = useContext(SelectedLeagueContext)
    if (!context) {
        throw new Error('useSelectedLeague must be used within a SelectedLeagueProvider')
    }
    return context
}
