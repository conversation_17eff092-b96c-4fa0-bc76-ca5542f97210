'use client'

import {usePathname, useRouter} from 'next/navigation'

import React, {createContext, ReactNode, useContext, useEffect, useState} from 'react'
import {useDebouncedCallback} from 'use-debounce'
interface SelectedSportContextType {
    selectedSport: string
    setSelectedSport: (sport: string) => void
}

const SelectedSportContext = createContext<SelectedSportContextType | undefined>(undefined)

const validSports = [
    'soccer',
    'cricket',
    'hockey',
    'tennis',
    'basketball',
    'baseball',
    'rugby',
    'golf',
]

export const SelectedSportProvider: React.FC<{children: ReactNode}> = ({children}) => {
    const router = useRouter()
    const pathname = usePathname()

    // TODO: Handle invalid sport paths
    const getSportFromPath = () => {
        const pathParts = pathname?.split('/').filter(Boolean)
        const sport = pathParts?.[0] || 'soccer'
        return validSports.includes(sport) ? sport : 'soccer'
    }

    const [selectedSport, setSelectedSport] = useState<string>(getSportFromPath())

    useEffect(() => {
        const sportFromPath = getSportFromPath()

        if (sportFromPath !== selectedSport) {
            setSelectedSport(sportFromPath)
        }
    }, [pathname, selectedSport])

    const handleSetSelectedSport = useDebouncedCallback((sport: string) => {
        setSelectedSport(sport)
        router.push(`/${sport}`)
    }, 100)

    const contextValue = React.useMemo(
        () => ({selectedSport, setSelectedSport: handleSetSelectedSport}),
        [selectedSport]
    )

    return (
        <SelectedSportContext.Provider value={contextValue}>
            {children}
        </SelectedSportContext.Provider>
    )
}

export const useSelectedSport = () => {
    const context = useContext(SelectedSportContext)
    if (!context) {
        throw new Error('useSelectedSport must be used within a SelectedSportProvider')
    }
    return context
}
