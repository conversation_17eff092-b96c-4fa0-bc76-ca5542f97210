'use client'

import {
    extractPageFromPath,
    generateHomepagePageUrl,
    generateLeaguePageUrl,
} from '@/utils/pagination'
import {usePathname, useRouter} from 'next/navigation'
import React, {createContext, ReactNode, useContext, useEffect, useState} from 'react'
import {useSelectedLeague} from './SelectedLeagueContext'
import {useSelectedSport} from './SelectedSportContext'

interface PaginationContextType {
    currentPage: number
    setCurrentPage: (page: number) => void
    navigateToPage: (page: number) => void
}

const PaginationContext = createContext<PaginationContextType | undefined>(undefined)

export const PaginationProvider: React.FC<{children: ReactNode}> = ({children}) => {
    const router = useRouter()
    const pathname = usePathname()
    const {selectedSport} = useSelectedSport()
    const {selectedLeague} = useSelectedLeague()

    const [currentPage, setCurrentPage] = useState<number>(() =>
        extractPageFromPath(pathname ?? '')
    )

    useEffect(() => {
        const pageFromPath = extractPageFromPath(pathname ?? '')
        if (pageFromPath !== currentPage) {
            setCurrentPage(pageFromPath)
        }
    }, [pathname, currentPage])

    const navigateToPage = (page: number) => {
        setCurrentPage(page)

        let url: string
        if (selectedLeague === 'all') {
            url = generateHomepagePageUrl(selectedSport, page)
        } else {
            url = generateLeaguePageUrl(selectedSport, selectedLeague, page)
        }

        router.push(url)
    }

    const contextValue = React.useMemo(
        () => ({
            currentPage,
            setCurrentPage,
            navigateToPage,
        }),
        [currentPage]
    )

    return <PaginationContext.Provider value={contextValue}>{children}</PaginationContext.Provider>
}

export const usePagination = () => {
    const context = useContext(PaginationContext)
    if (!context) {
        throw new Error('usePagination must be used within a PaginationProvider')
    }
    return context
}
