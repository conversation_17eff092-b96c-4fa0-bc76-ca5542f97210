'use client'

import {QueryClient, QueryClientProvider} from '@tanstack/react-query'
import {useState} from 'react'

export function ReactQueryProvider({children}: {children: React.ReactNode}) {
    const [queryClient] = useState(
        () =>
            new QueryClient({
                defaultOptions: {
                    queries: {
                        staleTime: 5 * 60 * 1000, // 5 minutes - matches Redis cache TTL
                        gcTime: 10 * 60 * 1000, // 10 minutes - keep in memory longer
                        refetchOnWindowFocus: false,
                        refetchOnMount: false, // Don't refetch if data is fresh
                        retry: 2, // Retry failed requests twice
                        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
                    },
                },
            })
    )

    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
}
