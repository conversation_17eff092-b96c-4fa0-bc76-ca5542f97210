import type {NextConfig} from 'next'

const nextConfig: NextConfig = {
    // Enable experimental features for better performance
    experimental: {
        optimizePackageImports: ['@tanstack/react-query', 'react-loading-skeleton'],
        // Removed optimizeCss as it requires additional dependencies
    },

    // Compression and optimization
    compress: true,
    poweredByHeader: false,
    generateEtags: false,

    // Bundle optimization
    webpack: (config, {dev, isServer}) => {
        // Optimize bundle size
        if (!dev && !isServer) {
            config.optimization.splitChunks = {
                chunks: 'all',
                cacheGroups: {
                    vendor: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendors',
                        chunks: 'all',
                    },
                },
            }
        }
        return config
    },

    // Image optimization
    images: {
        domains: ['img.youtube.com', 'i.ytimg.com'],
        formats: ['image/avif', 'image/webp'], // AVIF first for better compression
        minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days cache
        deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
        imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
        dangerouslyAllowSVG: false,
        contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    },

    // Headers for better caching
    async headers() {
        return [
            {
                source: '/api/:path*',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: 'public, s-maxage=300, stale-while-revalidate=600',
                    },
                ],
            },
            {
                source: '/(.*)',
                headers: [
                    {
                        key: 'X-Content-Type-Options',
                        value: 'nosniff',
                    },
                    {
                        key: 'X-Frame-Options',
                        value: 'DENY',
                    },
                    {
                        key: 'X-XSS-Protection',
                        value: '1; mode=block',
                    },
                ],
            },
        ]
    },
}

export default nextConfig
