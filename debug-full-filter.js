// Debug script to test full filtering logic
function ExtractTeamsFromTitle(title) {
    const normalizedTitle = title
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // remove accents
        .replace(/['']/g, "'")
        .replace(/[\u2013\u2014]/g, '-') // normalize long dashes
        .toLowerCase()

    const separators = [' vs. ', ' vs ', ' v. ', ' v ', ' - ']
    let splitIndex = -1
    let separatorLength = 0

    // Find separator in normalized title
    for (const sep of separators) {
        const index = normalizedTitle.indexOf(sep)
        if (index !== -1) {
            splitIndex = index
            separatorLength = sep.length
            break
        }
    }

    if (splitIndex === -1) return []

    // Split original title using the found index
    const leftOriginal = title.slice(0, splitIndex)
    const rightOriginal = title.slice(splitIndex + separatorLength)

    const stopWords = new Set([
        'extended',
        'highlights',
        'match',
        'live',
        'preview',
        'reaction',
        'recap',
        'uel',
        'ucl',
        'epl',
        'serie',
        'bundesliga',
        'ligue',
        'mls',
        'la',
        'liga',
        'phase',
        'leg',
        'final',
        'quarter-final',
        'semi-final',
        'knockout',
        'group',
        'round',
        'play-off',
        'md',
        'el',
        'clasico',
        'premier',
        'league',
        '1',
        '2',
        'a',
    ])

    const cleanTeamName = (part) => {
        const words = part.trim().split(/\s+/)
        const nameParts = []

        for (const word of words) {
            // Remove trailing punctuation but keep letters with accents and special characters
            const cleaned = word.replace(/[^\w\-/\p{L}]+$/giu, '')
            if (!cleaned) break
            if (stopWords.has(cleaned.toLowerCase())) break
            // Allow letters (including accented), numbers, dots, hyphens, and slashes
            if (/^[\w.\-/\p{L}]+$/iu.test(cleaned)) {
                nameParts.push(cleaned.charAt(0).toUpperCase() + cleaned.slice(1))
            } else {
                break
            }
        }

        return nameParts.join(' ')
    }

    const teamA = cleanTeamName(leftOriginal)
    const teamB = cleanTeamName(rightOriginal)

    return teamA && teamB ? [teamA, teamB] : []
}

function testBundesligaFiltering(originalTitle) {
    console.log('Original title:', originalTitle)
    console.log('='.repeat(80))

    // Step 1: Bundesliga prefix removal
    let processedTitle = originalTitle
    const league = 'bundesliga'

    if (league.toLowerCase() === 'bundesliga') {
        // Look for pattern: "Prefix | TEAM1 - TEAM2 | ..."
        // Try different pipe patterns: " | ", " |", "| ", "|"
        const pipePatterns = [' | ', ' |', '| ', '|']
        let pipeIndex = -1
        let pipeLength = 0

        for (const pattern of pipePatterns) {
            const index = processedTitle.indexOf(pattern)
            if (index !== -1) {
                pipeIndex = index
                pipeLength = pattern.length
                console.log('Found pipe pattern:', pattern, 'at index:', index)
                break
            }
        }

        if (pipeIndex !== -1) {
            const beforePipe = processedTitle.substring(0, pipeIndex)
            console.log('Before pipe:', beforePipe)
            const teamSeparators = [' - ', ' vs. ', ' vs ', ' v. ', ' v ']
            const hasTeamSeparator = teamSeparators.some((sep) => beforePipe.includes(sep))
            console.log('Has team separator in prefix:', hasTeamSeparator)

            // Only remove prefix if it doesn't contain team names (no separators)
            if (!hasTeamSeparator) {
                processedTitle = processedTitle.substring(pipeIndex + pipeLength)
                console.log('Removed prefix, new title:', processedTitle)
            } else {
                console.log('Keeping prefix because it contains team separator')
            }
        } else {
            console.log('No pipe pattern found')
        }
    }

    // Step 2: Extract teams
    console.log('\nExtracting teams from:', processedTitle)
    const teams = ExtractTeamsFromTitle(processedTitle)
    console.log('Extracted teams:', teams)
    console.log('Teams count:', teams.length)

    // Step 3: Normalize title for keyword checking
    const normalizedTitle = originalTitle
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .toLowerCase()
    console.log('\nNormalized title for keyword checking:', normalizedTitle)

    // Step 4: Check highlight keywords
    const highlightKeywords = ['highlights', 'extended highlights', 'match highlights']
    const highlightPresent = highlightKeywords.some((kw) => normalizedTitle.includes(kw))
    console.log('Highlight keywords present:', highlightPresent)
    highlightKeywords.forEach((kw) => {
        console.log(`  "${kw}":`, normalizedTitle.includes(kw))
    })

    // Step 5: Check included keywords (Bundesliga config)
    const keywords = {
        included: ['Highlights', '-', 'Bundesliga'],
        excluded: ['2. Bundesliga', 'Bundesliga 2 '],
    }

    const allIncludedPresent = keywords.included.every((k) =>
        normalizedTitle.includes(k.toLowerCase())
    )
    console.log('\nIncluded keywords check:', allIncludedPresent)
    keywords.included.forEach((k) => {
        console.log(`  "${k.toLowerCase()}":`, normalizedTitle.includes(k.toLowerCase()))
    })

    // Step 6: Check excluded keywords
    const anyExcludedPresent = keywords.excluded.some((k) =>
        normalizedTitle.includes(k.toLowerCase())
    )
    console.log('\nExcluded keywords check (should be false):', anyExcludedPresent)
    keywords.excluded.forEach((k) => {
        console.log(`  "${k.toLowerCase()}":`, normalizedTitle.includes(k.toLowerCase()))
    })

    // Step 7: Final decision
    const shouldInclude =
        teams.length === 2 && allIncludedPresent && highlightPresent && !anyExcludedPresent

    console.log('\n' + '='.repeat(80))
    console.log('FINAL DECISION:')
    console.log('  Teams count === 2:', teams.length === 2)
    console.log('  All included present:', allIncludedPresent)
    console.log('  Highlight present:', highlightPresent)
    console.log('  No excluded present:', !anyExcludedPresent)
    console.log('  SHOULD INCLUDE:', shouldInclude)

    return shouldInclude
}

// Test the problematic title
const title =
    'Draw in Tough Match! | Mainz 05 - Union Berlin 1-1 | Highlights | Matchday 1 – Bundesliga 2024/25'
testBundesligaFiltering(title)
